"""
路亚基地相关的Pydantic模型
用于API请求和响应的数据验证
"""

from typing import Any
from pydantic import BaseModel, Field
from .common import SearchParams


class FishingLocationSearchParams(SearchParams):
    """路亚基地搜索参数"""
    province_code: str | None = Field(default=None, description="省份代码")
    city_code: str | None = Field(default=None, description="城市代码")
    area_code: str | None = Field(default=None, description="区县代码")
    types: list[str] | None = Field(default=None, description="基地类型筛选")
    fish_species: list[str] | None = Field(default=None, description="鱼种筛选")


class FishingLocationResponse(BaseModel):
    """路亚基地响应模型"""
    id: int = Field(description="基地ID")
    name: str = Field(description="基地名称")
    types: list[str] | None = Field(default=None, description="基地类型")
    province_code: str | None = Field(default=None, description="省份代码")
    city_code: str | None = Field(default=None, description="城市代码")
    area_code: str | None = Field(default=None, description="区县代码")
    coordinates: dict[str, Any] | None = Field(default=None, description="坐标信息")
    desc: str | None = Field(default=None, description="基地介绍")
    contact: str | None = Field(default=None, description="联系方式")
    fish_species: list[str] | None = Field(default=None, description="可钓鱼种")


class FishingLocationDetailResponse(FishingLocationResponse):
    """路亚基地详情响应模型"""
    # 继承基础响应模型，可以在这里添加详情页特有的字段
    pass


class FishingLocationListItem(BaseModel):
    """路亚基地列表项模型"""
    id: int = Field(description="基地ID")
    name: str = Field(description="基地名称")
    types: list[str] | None = Field(default=None, description="基地类型")
    province_code: str | None = Field(default=None, description="省份代码")
    city_code: str | None = Field(default=None, description="城市代码")
    area_code: str | None = Field(default=None, description="区县代码")
    fish_species: list[str] | None = Field(default=None, description="可钓鱼种")
    contact: str | None = Field(default=None, description="联系方式")


class FishingLocationCreate(BaseModel):
    """创建路亚基地请求模型"""
    name: str = Field(max_length=50, description="名称")
    types: list[str] | None = Field(default=None, description="类型")
    province_code: str | None = Field(default=None, max_length=15, description="省份")
    city_code: str | None = Field(default=None, max_length=15, description="城市")
    area_code: str | None = Field(default=None, max_length=15, description="区县")
    coordinates: dict[str, Any] | None = Field(default=None, description="坐标")
    desc: str | None = Field(default=None, description="介绍")
    contact: str | None = Field(default=None, max_length=255, description="联系方式")
    fish_species: list[str] | None = Field(default=None, description="鱼种")


class FishingLocationUpdate(BaseModel):
    """更新路亚基地请求模型"""
    name: str | None = Field(default=None, max_length=50, description="名称")
    types: list[str] | None = Field(default=None, description="类型")
    province_code: str | None = Field(default=None, max_length=15, description="省份")
    city_code: str | None = Field(default=None, max_length=15, description="城市")
    area_code: str | None = Field(default=None, max_length=15, description="区县")
    coordinates: dict[str, Any] | None = Field(default=None, description="坐标")
    desc: str | None = Field(default=None, description="介绍")
    contact: str | None = Field(default=None, max_length=255, description="联系方式")
    fish_species: list[str] | None = Field(default=None, description="鱼种")
