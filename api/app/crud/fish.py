"""
鱼种CRUD操作
"""

from sqlmodel import Session, select, or_, func
from app.crud.base import CRUDBase
from app.models.fish import Fish, FishCreate, FishUpdate


class CRUDFish(CRUDBase[Fish, FishCreate, FishUpdate]):
    """鱼种CRUD操作类"""

    def search(
        self,
        db: Session,
        /,
        *,
        keyword: str | None = None,
        skip: int = 0,
        limit: int = 100
    ) -> list[Fish]:
        """
        搜索鱼种

        Args:
            db: 数据库会话
            keyword: 搜索关键词
            skip: 跳过记录数
            limit: 限制记录数

        Returns:
            list[Fish]: 鱼种列表
        """
        statement = select(Fish)

        # 关键词搜索
        if keyword:
            statement = statement.where(
                or_(
                    Fish.name.contains(keyword),
                    Fish.alias_name.contains(keyword)
                )
            )

        # 分页
        statement = statement.offset(skip).limit(limit)

        return list(db.exec(statement).all())

    def count_search(
        self,
        db: Session,
        /,
        *,
        keyword: str | None = None
    ) -> int:
        """
        统计搜索结果数量

        Args:
            db: 数据库会话
            keyword: 搜索关键词

        Returns:
            int: 记录总数
        """
        statement = select(Fish)

        # 关键词搜索
        if keyword:
            statement = statement.where(
                or_(
                    Fish.name.contains(keyword),
                    Fish.alias_name.contains(keyword)
                )
            )

        results = db.exec(statement).all()
        return len(results)

    def get_by_name(self, db: Session, /, *, name: str) -> Fish | None:
        """根据名称获取鱼种"""
        statement = select(Fish).where(Fish.name == name)
        return db.exec(statement).first()

    def get_by_alias_name(self, db: Session, /, *, alias_name: str) -> Fish | None:
        """根据别名获取鱼种"""
        statement = select(Fish).where(Fish.alias_name == alias_name)
        return db.exec(statement).first()

    def get_popular(
        self,
        db: Session,
        /,
        *,
        limit: int = 10
    ) -> list[Fish]:
        """
        获取热门鱼种

        Args:
            db: 数据库会话
            limit: 限制记录数

        Returns:
            list[Fish]: 热门鱼种列表
        """
        # 这里可以根据实际业务逻辑实现，比如按照某个热度字段排序
        # 目前简单按照ID排序返回前N条
        statement = select(Fish).limit(limit)
        return list(db.exec(statement).all())


# 创建鱼种CRUD实例
fish = CRUDFish(Fish)
