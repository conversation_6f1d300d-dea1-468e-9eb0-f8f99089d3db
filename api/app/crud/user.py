"""
用户CRUD操作
"""

import time

from sqlmodel import Session, select
from app.crud.base import CRUDBase
from app.models.user import User, UserCreate, UserUpdate
from app.core.security import get_password_hash, verify_password, generate_auth_key


class CRUDUser(CRUDBase[User, UserCreate, UserUpdate]):
    """用户CRUD操作类"""

    def get_by_username(self, db: Session, /, *, username: str) -> User | None:
        """根据用户名获取用户"""
        statement = select(User).where(User.username == username)
        return db.exec(statement).first()

    def get_by_email(self, db: Session, /, *, email: str) -> User | None:
        """根据邮箱获取用户"""
        statement = select(User).where(User.email == email)
        return db.exec(statement).first()

    def get_by_phone(self, db: Session, /, *, phone: str) -> User | None:
        """根据手机号获取用户"""
        statement = select(User).where(User.phone == phone)
        return db.exec(statement).first()

    def get_by_username_or_email_or_phone(
        self, db: Session, /, *, identifier: str
    ) -> User | None:
        """根据用户名、邮箱或手机号获取用户"""
        statement = select(User).where(
            (User.username == identifier) |
            (User.email == identifier) |
            (User.phone == identifier)
        )
        return db.exec(statement).first()

    def create(self, db: Session, /, *, obj_in: UserCreate) -> User:
        """创建用户"""
        current_time = int(time.time())
        
        # 创建用户对象
        db_obj = User(
            username=obj_in.username,
            screen_name=obj_in.screen_name,
            auth_key=generate_auth_key(),
            password_hash=get_password_hash(obj_in.password),
            phone=obj_in.phone,
            email=obj_in.email,
            status=1,  # 默认启用状态
            created_at=current_time,
            updated_at=current_time
        )
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(
        self, db: Session, /, *, db_obj: User, obj_in: UserUpdate | dict
    ) -> User:
        """更新用户信息"""
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
        
        # 更新时间戳
        update_data["updated_at"] = int(time.time())
        
        return super().update(db, db_obj=db_obj, obj_in=update_data)

    def authenticate(
        self, db: Session, /, *, username: str, password: str
    ) -> User | None:
        """验证用户身份"""
        user = self.get_by_username_or_email_or_phone(db, identifier=username)
        if not user:
            return None
        if not verify_password(password, user.password_hash):
            return None
        return user

    def is_active(self, user: User, /) -> bool:
        """检查用户是否激活"""
        return user.status == 1

    def is_superuser(self, user: User, /) -> bool:
        """检查用户是否为超级用户"""
        # 这里可以根据实际需求实现超级用户逻辑
        # 比如检查用户名是否为admin，或者添加专门的字段
        return user.username == "admin"

    def update_password(
        self, db: Session, /, *, user: User, new_password: str
    ) -> User:
        """更新用户密码"""
        hashed_password = get_password_hash(new_password)
        user.password_hash = hashed_password
        user.updated_at = int(time.time())
        
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def set_password_reset_token(
        self, db: Session, /, *, user: User, token: str
    ) -> User:
        """设置密码重置令牌"""
        user.password_reset_token = token
        user.updated_at = int(time.time())
        
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def get_by_password_reset_token(
        self, db: Session, /, *, token: str
    ) -> User | None:
        """根据密码重置令牌获取用户"""
        statement = select(User).where(User.password_reset_token == token)
        return db.exec(statement).first()

    def set_verification_token(
        self, db: Session, /, *, user: User, token: str
    ) -> User:
        """设置邮箱验证令牌"""
        user.verification_token = token
        user.updated_at = int(time.time())

        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def get_by_verification_token(
        self, db: Session, /, *, token: str
    ) -> User | None:
        """根据验证令牌获取用户"""
        statement = select(User).where(User.verification_token == token)
        return db.exec(statement).first()

    def verify_email(self, db: Session, /, *, user: User) -> User:
        """验证用户邮箱"""
        user.verification_token = None
        user.updated_at = int(time.time())

        db.add(user)
        db.commit()
        db.refresh(user)
        return user


# 创建用户CRUD实例
user = CRUDUser(User)
