"""
路亚竿CRUD操作
"""

from decimal import Decimal
from sqlmodel import Session, select, or_, and_, func
from app.crud.base import CRUDBase
from app.models.rod import Rod, RodCreate, RodUpdate
from app.models.rod_item import RodItem, RodItemCreate, RodItemUpdate


class CRUDRod(CRUDBase[Rod, RodCreate, RodUpdate]):
    """路亚竿CRUD操作类"""
    
    def search(
        self,
        db: Session,
        /,
        *,
        keyword: str | None = None,
        company_id: int | None = None,
        series_id: int | None = None,
        skip: int = 0,
        limit: int = 100
    ) -> list[Rod]:
        """搜索路亚竿"""
        statement = select(Rod)

        # 关键词搜索
        if keyword:
            statement = statement.where(
                or_(
                    Rod.name.contains(keyword),
                    Rod.desc.contains(keyword)
                )
            )

        # 品牌筛选
        if company_id:
            statement = statement.where(Rod.company_id == company_id)

        # 系列筛选
        if series_id:
            statement = statement.where(Rod.series_id == series_id)

        statement = statement.offset(skip).limit(limit)
        return list(db.exec(statement).all())

    def get_by_uuid(self, db: Session, uuid: str, /) -> Rod | None:
        """根据UUID获取路亚竿"""
        return self.get_by_field(db, "uuid", uuid)
    
    def get_by_company(
        self,
        db: Session,
        company_id: int,
        /,
        *,
        skip: int = 0,
        limit: int = 100
    ) -> list[Rod]:
        """根据品牌获取路亚竿"""
        return self.get_multi_by_field(db, "company_id", company_id, skip=skip, limit=limit)

    def get_by_series(
        self,
        db: Session,
        series_id: int,
        /,
        *,
        skip: int = 0,
        limit: int = 100
    ) -> list[Rod]:
        """根据系列获取路亚竿"""
        return self.get_multi_by_field(db, "series_id", series_id, skip=skip, limit=limit)


class CRUDRodItem(CRUDBase[RodItem, RodItemCreate, RodItemUpdate]):
    """路亚竿型号CRUD操作类"""
    
    def search(
        self,
        db: Session,
        /,
        *,
        keyword: str | None = None,
        rod_id: int | None = None,
        min_lure_wt: Decimal | None = None,
        max_lure_wt: Decimal | None = None,
        action: str | None = None,
        power: str | None = None,
        reel_seat_type: str | None = None,
        skip: int = 0,
        limit: int = 100
    ) -> list[RodItem]:
        """搜索路亚竿型号"""
        statement = select(RodItem)

        # 关键词搜索
        if keyword:
            statement = statement.where(
                or_(
                    RodItem.name.contains(keyword),
                    RodItem.sub_name.contains(keyword),
                    RodItem.desc.contains(keyword),
                    RodItem.short_desc.contains(keyword)
                )
            )

        # 路亚竿筛选
        if rod_id:
            statement = statement.where(RodItem.rod_id == rod_id)

        # 饵重筛选
        if min_lure_wt is not None:
            statement = statement.where(RodItem.min_lure_wt.is_not(None) & (RodItem.min_lure_wt >= min_lure_wt))
        if max_lure_wt is not None:
            statement = statement.where(RodItem.max_lure_wt.is_not(None) & (RodItem.max_lure_wt <= max_lure_wt))

        # 调性筛选
        if action:
            statement = statement.where(RodItem.action == action)

        # 硬度筛选
        if power:
            statement = statement.where(RodItem.power == power)

        # 枪柄/直柄筛选
        if reel_seat_type:
            statement = statement.where(RodItem.reel_seat_type == reel_seat_type)

        statement = statement.offset(skip).limit(limit)
        return list(db.exec(statement).all())

    def get_by_rod_id(
        self,
        db: Session,
        rod_id: int,
        /,
        *,
        skip: int = 0,
        limit: int = 100
    ) -> list[RodItem]:
        """根据路亚竿ID获取型号列表"""
        return self.get_multi_by_field(db, "rod_id", rod_id, skip=skip, limit=limit)

    def get_by_uuid(self, db: Session, uuid: str, /) -> RodItem | None:
        """根据UUID获取路亚竿型号"""
        return self.get_by_field(db, "uuid", uuid)


# 创建CRUD实例
rod = CRUDRod(Rod)
rod_item = CRUDRodItem(RodItem)
