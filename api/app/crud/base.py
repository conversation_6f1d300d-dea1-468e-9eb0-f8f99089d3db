"""
CRUD基础类
提供通用的数据库操作方法
"""

from typing import Any, Generic, Type, TypeVar
from sqlmodel import SQLModel, Session, select
from pydantic import BaseModel

ModelType = TypeVar("ModelType", bound=SQLModel)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """CRUD基础类"""
    
    def __init__(self, model: Type[ModelType]):
        """
        初始化CRUD对象
        
        Args:
            model: SQLModel模型类
        """
        self.model = model

    def get(self, db: Session, *, id: Any) -> ModelType | None:
        """根据ID获取单个记录"""
        return db.get(self.model, id)

    def get_multi(
        self,
        db: Session,
        /, *,
        skip: int = 0,
        limit: int = 100
    ) -> list[ModelType]:
        """获取多个记录"""
        statement = select(self.model).offset(skip).limit(limit)
        return list(db.exec(statement).all())

    def create(self, db: Session, /, *, obj_in: CreateSchemaType) -> ModelType:
        """创建新记录"""
        obj_in_data = obj_in.model_dump()
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(
        self,
        db: Session,
        /,
        *,
        db_obj: ModelType,
        obj_in: UpdateSchemaType | dict[str, Any]
    ) -> ModelType:
        """更新记录"""
        obj_data = db_obj.model_dump()
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)

        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])

        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def remove(self, db: Session, /, *, id: int) -> ModelType | None:
        """删除记录"""
        obj = db.get(self.model, id)
        if obj:
            db.delete(obj)
            db.commit()
        return obj

    def count(self, db: Session) -> int:
        """获取记录总数"""
        statement = select(self.model)
        return len(db.exec(statement).all())

    def get_by_field(
        self,
        db: Session,
        field_name: str,
        field_value: Any,
        /
    ) -> ModelType | None:
        """根据字段值获取记录"""
        statement = select(self.model).where(getattr(self.model, field_name) == field_value)
        return db.exec(statement).first()

    def get_multi_by_field(
        self,
        db: Session,
        field_name: str,
        field_value: Any,
        /,
        *,
        skip: int = 0,
        limit: int = 100
    ) -> list[ModelType]:
        """根据字段值获取多个记录"""
        statement = (
            select(self.model)
            .where(getattr(self.model, field_name) == field_value)
            .offset(skip)
            .limit(limit)
        )
        return list(db.exec(statement).all())
