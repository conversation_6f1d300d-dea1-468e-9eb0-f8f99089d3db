"""
鱼种模型
对应数据库中的 fish 表
"""

from sqlmodel import SQLModel, Field


class Fish(SQLModel, table=True):
    """鱼种表"""
    __tablename__ = "fish"

    id: int | None = Field(default=None, primary_key=True, description="主键ID")
    name: str = Field(max_length=30, description="鱼种名称")
    alias_name: str | None = Field(default=None, max_length=255, description="别名")


class FishCreate(SQLModel):
    """创建鱼种时的数据模型"""
    name: str = Field(max_length=30, description="鱼种名称")
    alias_name: str | None = Field(default=None, max_length=255, description="别名")


class FishUpdate(SQLModel):
    """更新鱼种时的数据模型"""
    name: str | None = Field(default=None, max_length=30, description="鱼种名称")
    alias_name: str | None = Field(default=None, max_length=255, description="别名")


class FishRead(SQLModel):
    """读取鱼种时的数据模型"""
    id: int
    name: str
    alias_name: str | None = None
