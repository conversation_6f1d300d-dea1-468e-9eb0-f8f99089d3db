"""
路亚基地业务逻辑服务
"""

from fastapi import HTTPException, status
from sqlmodel import Session
from app.crud import fishing_location
from app.schemas.fishing_location import (
    FishingLocationSearchParams,
    FishingLocationResponse,
    FishingLocationDetailResponse,
    FishingLocationListItem,
    FishingLocationCreate,
    FishingLocationUpdate
)
from app.schemas.common import PaginationModel


class FishingLocationService:
    """路亚基地业务逻辑服务类"""
    
    def search_fishing_locations(
        self,
        db: Session,
        search_params: FishingLocationSearchParams
    ) -> tuple[list[FishingLocationListItem], PaginationModel]:
        """
        搜索路亚基地

        Args:
            db: 数据库会话
            search_params: 搜索参数

        Returns:
            tuple[list[FishingLocationListItem], PaginationModel]: 基地列表和分页信息
        """
        # 计算偏移量
        skip = (search_params.page - 1) * search_params.page_size
        
        # 搜索基地
        locations = fishing_location.search(
            db,
            keyword=search_params.keyword,
            province_code=search_params.province_code,
            city_code=search_params.city_code,
            area_code=search_params.area_code,
            types=search_params.types,
            fish_species=search_params.fish_species,
            skip=skip,
            limit=search_params.page_size
        )
        
        # 统计总数
        total = fishing_location.count_search(
            db,
            keyword=search_params.keyword,
            province_code=search_params.province_code,
            city_code=search_params.city_code,
            area_code=search_params.area_code,
            types=search_params.types,
            fish_species=search_params.fish_species
        )
        
        # 转换为响应模型
        location_items = [
            FishingLocationListItem(
                id=location.id,
                name=location.name,
                types=location.types,
                province_code=location.province_code,
                city_code=location.city_code,
                area_code=location.area_code,
                fish_species=location.fish_species,
                contact=location.contact
            )
            for location in locations
        ]
        
        # 计算分页信息
        total_pages = (total + search_params.page_size - 1) // search_params.page_size
        pagination = PaginationModel(
            page=search_params.page,
            page_size=search_params.page_size,
            total=total,
            total_pages=total_pages
        )
        
        return location_items, pagination
    
    def get_fishing_location_detail(
        self,
        db: Session,
        location_id: int
    ) -> FishingLocationDetailResponse | None:
        """
        获取路亚基地详情

        Args:
            db: 数据库会话
            location_id: 基地ID

        Returns:
            FishingLocationDetailResponse | None: 基地详情
        """
        location = fishing_location.get(db, location_id)
        if not location:
            return None
        
        return FishingLocationDetailResponse(
            id=location.id,
            name=location.name,
            types=location.types,
            province_code=location.province_code,
            city_code=location.city_code,
            area_code=location.area_code,
            coordinates=location.coordinates,
            desc=location.desc,
            contact=location.contact,
            fish_species=location.fish_species
        )
    
    def get_fishing_locations_by_region(
        self,
        db: Session,
        province_code: str | None = None,
        city_code: str | None = None,
        area_code: str | None = None,
        page: int = 1,
        page_size: int = 20
    ) -> tuple[list[FishingLocationListItem], PaginationModel]:
        """
        根据地区获取路亚基地

        Args:
            db: 数据库会话
            province_code: 省份代码
            city_code: 城市代码
            area_code: 区县代码
            page: 页码
            page_size: 每页数量

        Returns:
            tuple[list[FishingLocationListItem], PaginationModel]: 基地列表和分页信息
        """
        skip = (page - 1) * page_size
        
        locations = fishing_location.get_by_region(
            db,
            province_code=province_code,
            city_code=city_code,
            area_code=area_code,
            skip=skip,
            limit=page_size
        )
        
        # 这里简化处理，实际应该有专门的计数方法
        total = len(fishing_location.get_by_region(
            db,
            province_code=province_code,
            city_code=city_code,
            area_code=area_code,
            skip=0,
            limit=10000  # 大数值获取所有记录用于计数
        ))
        
        location_items = [
            FishingLocationListItem(
                id=location.id,
                name=location.name,
                types=location.types,
                province_code=location.province_code,
                city_code=location.city_code,
                area_code=location.area_code,
                fish_species=location.fish_species,
                contact=location.contact
            )
            for location in locations
        ]
        
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationModel(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages
        )
        
        return location_items, pagination

    def create_fishing_location(
        self,
        db: Session,
        location_in: FishingLocationCreate
    ) -> FishingLocationResponse:
        """
        创建路亚基地

        Args:
            db: 数据库会话
            location_in: 基地创建信息

        Returns:
            FishingLocationResponse: 创建的基地信息
        """
        # 创建基地
        location = fishing_location.create(db, obj_in=location_in)

        return FishingLocationResponse(
            id=location.id,
            name=location.name,
            types=location.types,
            province_code=location.province_code,
            city_code=location.city_code,
            area_code=location.area_code,
            coordinates=location.coordinates,
            desc=location.desc,
            contact=location.contact,
            fish_species=location.fish_species
        )

    def update_fishing_location(
        self,
        db: Session,
        location_id: int,
        location_in: FishingLocationUpdate
    ) -> FishingLocationResponse | None:
        """
        更新路亚基地

        Args:
            db: 数据库会话
            location_id: 基地ID
            location_in: 基地更新信息

        Returns:
            FishingLocationResponse | None: 更新后的基地信息
        """
        # 获取基地
        location = fishing_location.get(db, id=location_id)
        if not location:
            return None

        # 更新基地
        updated_location = fishing_location.update(db, db_obj=location, obj_in=location_in)

        return FishingLocationResponse(
            id=updated_location.id,
            name=updated_location.name,
            types=updated_location.types,
            province_code=updated_location.province_code,
            city_code=updated_location.city_code,
            area_code=updated_location.area_code,
            coordinates=updated_location.coordinates,
            desc=updated_location.desc,
            contact=updated_location.contact,
            fish_species=updated_location.fish_species
        )

    def delete_fishing_location(
        self,
        db: Session,
        location_id: int
    ) -> bool:
        """
        删除路亚基地

        Args:
            db: 数据库会话
            location_id: 基地ID

        Returns:
            bool: 是否删除成功
        """
        location = fishing_location.get(db, id=location_id)
        if not location:
            return False

        fishing_location.remove(db, id=location_id)
        return True


# 创建服务实例
fishing_location_service = FishingLocationService()
