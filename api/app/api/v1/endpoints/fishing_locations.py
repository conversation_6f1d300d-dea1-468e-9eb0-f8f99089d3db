"""
路亚基地API接口
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session
from app.db.session import get_db
from app.dependencies.auth import CurrentActiveUser
from app.services import fishing_location_service
from app.schemas.fishing_location import (
    FishingLocationSearchParams,
    FishingLocationDetailResponse,
    FishingLocationListItem,
    FishingLocationCreate,
    FishingLocationUpdate,
    FishingLocationResponse
)
from app.schemas.common import PaginatedResponseModel, ResponseModel

router = APIRouter()


@router.get("/search", response_model=PaginatedResponseModel[FishingLocationListItem])
async def search_fishing_locations(
    keyword: str = Query(None, description="搜索关键词"),
    province_code: str = Query(None, description="省份代码"),
    city_code: str = Query(None, description="城市代码"),
    area_code: str = Query(None, description="区县代码"),
    types: list[str] = Query(None, description="基地类型筛选"),
    fish_species: list[str] = Query(None, description="鱼种筛选"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """
    搜索路亚基地
    
    - **keyword**: 搜索关键词，会在基地名称、描述、联系方式中搜索
    - **province_code**: 省份代码筛选
    - **city_code**: 城市代码筛选
    - **area_code**: 区县代码筛选
    - **types**: 基地类型筛选（可多选）
    - **fish_species**: 鱼种筛选（可多选）
    - **page**: 页码，从1开始
    - **page_size**: 每页数量，最大100
    """
    search_params = FishingLocationSearchParams(
        keyword=keyword,
        province_code=province_code,
        city_code=city_code,
        area_code=area_code,
        types=types,
        fish_species=fish_species,
        page=page,
        page_size=page_size
    )
    
    locations, pagination = fishing_location_service.search_fishing_locations(db, search_params)
    
    return PaginatedResponseModel(
        code=200,
        message="success",
        data=locations,
        pagination=pagination
    )


@router.get("/{location_id}", response_model=ResponseModel[FishingLocationDetailResponse])
async def get_fishing_location_detail(
    location_id: int,
    db: Session = Depends(get_db)
):
    """
    获取路亚基地详情
    
    - **location_id**: 基地ID
    """
    location = fishing_location_service.get_fishing_location_detail(db, location_id)
    
    if not location:
        raise HTTPException(status_code=404, detail="路亚基地不存在")
    
    return ResponseModel(
        code=200,
        message="success",
        data=location
    )


@router.get("/region/list", response_model=PaginatedResponseModel[FishingLocationListItem])
async def get_fishing_locations_by_region(
    province_code: str = Query(None, description="省份代码"),
    city_code: str = Query(None, description="城市代码"),
    area_code: str = Query(None, description="区县代码"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """
    根据地区获取路亚基地列表
    
    - **province_code**: 省份代码
    - **city_code**: 城市代码
    - **area_code**: 区县代码
    - **page**: 页码，从1开始
    - **page_size**: 每页数量，最大100
    """
    locations, pagination = fishing_location_service.get_fishing_locations_by_region(
        db,
        province_code=province_code,
        city_code=city_code,
        area_code=area_code,
        page=page,
        page_size=page_size
    )
    
    return PaginatedResponseModel(
        code=200,
        message="success",
        data=locations,
        pagination=pagination
    )


@router.post("", response_model=ResponseModel[FishingLocationResponse])
async def create_fishing_location(
    location_in: FishingLocationCreate,
    current_user: CurrentActiveUser,
    db: Session = Depends(get_db)
):
    """
    创建路亚基地

    需要认证：Bearer Token

    - **name**: 名称
    - **types**: 类型（可选）
    - **province_code**: 省份代码（可选）
    - **city_code**: 城市代码（可选）
    - **area_code**: 区县代码（可选）
    - **coordinates**: 坐标（可选）
    - **desc**: 介绍（可选）
    - **contact**: 联系方式（可选）
    - **fish_species**: 鱼种（可选）
    """
    location = fishing_location_service.create_fishing_location(db, location_in=location_in)

    return ResponseModel(
        code=200,
        message="创建成功",
        data=location
    )


@router.put("/{location_id}", response_model=ResponseModel[FishingLocationResponse])
async def update_fishing_location(
    location_id: int,
    location_in: FishingLocationUpdate,
    current_user: CurrentActiveUser,
    db: Session = Depends(get_db)
):
    """
    更新路亚基地

    需要认证：Bearer Token

    - **location_id**: 基地ID
    - **name**: 名称（可选）
    - **types**: 类型（可选）
    - **province_code**: 省份代码（可选）
    - **city_code**: 城市代码（可选）
    - **area_code**: 区县代码（可选）
    - **coordinates**: 坐标（可选）
    - **desc**: 介绍（可选）
    - **contact**: 联系方式（可选）
    - **fish_species**: 鱼种（可选）
    """
    location = fishing_location_service.update_fishing_location(
        db, location_id=location_id, location_in=location_in
    )

    if not location:
        raise HTTPException(status_code=404, detail="路亚基地不存在")

    return ResponseModel(
        code=200,
        message="更新成功",
        data=location
    )


@router.delete("/{location_id}", response_model=ResponseModel[dict])
async def delete_fishing_location(
    location_id: int,
    current_user: CurrentActiveUser,
    db: Session = Depends(get_db)
):
    """
    删除路亚基地

    需要认证：Bearer Token

    - **location_id**: 基地ID
    """
    success = fishing_location_service.delete_fishing_location(db, location_id=location_id)

    if not success:
        raise HTTPException(status_code=404, detail="路亚基地不存在")

    return ResponseModel(
        code=200,
        message="删除成功",
        data={"deleted": True}
    )
