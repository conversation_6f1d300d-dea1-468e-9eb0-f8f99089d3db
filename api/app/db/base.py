"""
数据库基础模型类
定义 SQLModel ORM 模型的基础类，方便所有模型继承
"""

from datetime import datetime
from sqlmodel import SQLModel, Field


class TimestampMixin(SQLModel):
    """时间戳混入类"""
    created_at: int | None = Field(default=None, description="创建时间")
    updated_at: int | None = Field(default=None, description="更新时间")
    created_by: int | None = Field(default=None, description="创建者ID")
    updated_by: int | None = Field(default=None, description="更新者ID")


class BaseModel(TimestampMixin):
    """基础模型类，包含通用字段"""
    pass


def get_current_timestamp() -> int:
    """获取当前时间戳"""
    return int(datetime.now().timestamp())
